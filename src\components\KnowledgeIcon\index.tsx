import { memo } from 'react';
import { Center } from 'react-layout-kit';

import FileIcon from '@/components/FileIcon';
import RepoIcon from '@/components/RepoIcon';
import WorldbookIcon from '@/components/WorldbookIcon';
import { KnowledgeType } from '@/types/knowledgeBase';

interface KnowledgeIconProps {
  fileType?: string;
  name: string;
  size?: number | { file?: number; repo?: number; worldbook?: number };
  type: KnowledgeType;
}

const KnowledgeIcon = memo<KnowledgeIconProps>(({ type, size, fileType, name }) => {
  const repoSize = (typeof size === 'object' ? size.repo : size) || 24;
  const fileSize = (typeof size === 'object' ? size.file : size) || 24;
  const worldbookSize = (typeof size === 'object' ? size.worldbook : size) || 24;
  
  if (type === KnowledgeType.Worldbook) {
    return (
      <Center height={worldbookSize} width={worldbookSize}>
        <WorldbookIcon size={worldbookSize / 1.2} />
      </Center>
    );
  }
  return type === KnowledgeType.KnowledgeBase ? (
    <Center height={repoSize} width={repoSize}>
      <RepoIcon size={repoSize / 1.2} />
    </Center>
  ) : (
    <FileIcon fileName={name} fileType={fileType!} size={fileSize} />
  );
});

export default KnowledgeIcon;
