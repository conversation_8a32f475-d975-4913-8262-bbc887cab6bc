# LobeChat 世界书激活技术实现详细设计

## 🎯 概述

本文档基于v5架构方案，提供世界书激活功能的详细技术实现指导，包括核心接口设计、关键算法实现、数据流处理等技术细节。

## 📁 核心架构设计

### 1. 统一引擎接口设计

#### 基础引擎接口
```typescript
// src/server/services/worldbook/types.ts
export interface ActivationEngine {
  name: string;
  activate(context: ActivationContext, mode: ActivationMode): Promise<WorldbookEntry[]>;
}

export interface FilterEngine {
  process(entries: WorldbookEntry[], context: ActivationContext, mode: FilterMode): Promise<WorldbookEntry[]>;
}

export type ActivationMode = 'main' | 'recursion' | 'minActivation';
export type FilterMode = 'main' | 'recursion' | 'minActivation';

export interface ActivationContext {
  messages: OpenAIChatMessage[];
  topicId?: string;
  messageCount: number;
  runtimeData: WorldbookRuntimeData;
  scanText: string;
  recursionBuffer?: string;
  minActivationDepth?: number;
  userId: string;
  agentConfig?: any;
  userProfile?: any;
}
```

#### 运行时数据结构
```typescript
export interface WorldbookRuntimeData {
  timedEffects: Record<string, {
    sticky: number;        // 剩余sticky轮数
    cooldown: number;      // 剩余cooldown轮数
    delay: number;         // 剩余delay轮数
  }>;
  activationHistory: string[];  // 最近激活的条目ID
  lastActivation: number;       // 最后激活时间戳
  recursionDepth: number;       // 当前递归深度
  budgetUsed: number;          // 已使用的token预算
}

export interface WorldbookActivationResult {
  activatedEntries: WorldbookEntry[];
  processingTime: number;
  metadata: {
    constantCount: number;
    keywordCount: number;
    vectorCount: number;
    stickyCount: number;
    decoratorCount: number;
    recursionCount: number;
    minActivationCount: number;
    budgetUsed: number;
    totalBudget: number;
  };
}
```

### 2. 关键词匹配引擎实现

#### KeywordEngine核心算法
```typescript
// src/server/services/worldbook/engines/keyword-engine.ts
export class KeywordEngine implements ActivationEngine {
  name = 'keyword';

  async activate(context: ActivationContext, mode: ActivationMode): Promise<WorldbookEntry[]> {
    const entries = await this.getKeywordEntries(context);
    const scanText = this.buildScanText(context, mode);
    const activatedEntries: WorldbookEntry[] = [];

    for (const entry of entries) {
      if (await this.matchEntry(entry, scanText, context)) {
        activatedEntries.push(entry);
      }
    }

    return activatedEntries;
  }

  private buildScanText(context: ActivationContext, mode: ActivationMode): string {
    switch (mode) {
      case 'main':
        return this.extractMessagesText(context.messages);
      case 'recursion':
        return context.recursionBuffer || '';
      case 'minActivation':
        return this.buildExtendedScanText(context);
      default:
        return '';
    }
  }

  private async matchEntry(entry: WorldbookEntry, scanText: string, context: ActivationContext): Promise<boolean> {
    const processedKeys = this.processVariables(entry.keys, context);
    const processedSecondaryKeys = this.processVariables(entry.keysSecondary, context);
    
    const primaryMatch = this.evaluateKeywordLogic(processedKeys, scanText, entry.selectiveLogic);
    const secondaryMatch = processedSecondaryKeys.length > 0 
      ? this.evaluateKeywordLogic(processedSecondaryKeys, scanText, 'and_any')
      : true;

    return primaryMatch && secondaryMatch;
  }

  private evaluateKeywordLogic(keys: string[], text: string, logic: string): boolean {
    const matches = keys.map(key => this.matchKeyword(key, text));
    
    switch (logic) {
      case 'and_any':
        return matches.some(match => match);
      case 'and_all':
        return matches.every(match => match);
      case 'not_any':
        return !matches.some(match => match);
      case 'not_all':
        return !matches.every(match => match);
      default:
        return matches.some(match => match);
    }
  }

  private matchKeyword(keyword: string, text: string): boolean {
    // 处理正则表达式
    if (keyword.startsWith('/') && keyword.endsWith('/')) {
      const pattern = keyword.slice(1, -1);
      try {
        const regex = new RegExp(pattern, 'gi');
        return regex.test(text);
      } catch (error) {
        console.warn(`Invalid regex pattern: ${pattern}`, error);
        return false;
      }
    }

    // 全词匹配
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const wordBoundaryRegex = new RegExp(`\\b${escapedKeyword}\\b`, 'gi');
    return wordBoundaryRegex.test(text);
  }

  private processVariables(keys: string[], context: ActivationContext): string[] {
    return keys.map(key => {
      return key
        .replace(/\{\{char\}\}/gi, context.agentConfig?.title || 'Assistant')
        .replace(/\{\{user\}\}/gi, context.userProfile?.fullName || 'User')
        .replace(/\{\{time\}\}/gi, new Date().toLocaleTimeString('zh-CN', { hour12: false }))
        .replace(/\{\{date\}\}/gi, new Date().toLocaleDateString('zh-CN'));
    });
  }
}
```

### 3. 统一过滤引擎实现

#### FilterEngine四层过滤机制
```typescript
// src/server/services/worldbook/engines/filter-engine.ts
export class FilterEngine {
  async process(entries: WorldbookEntry[], context: ActivationContext, mode: FilterMode): Promise<WorldbookEntry[]> {
    let filteredEntries = [...entries];

    // 第一层：基础过滤
    filteredEntries = await this.applyBasicFilters(filteredEntries, context, mode);

    // 第二层：时间效果过滤
    filteredEntries = await this.applyTimedEffectsFilter(filteredEntries, context);

    // 第三层：包含组过滤
    filteredEntries = await this.applyInclusionGroupFilter(filteredEntries, context);

    // 第四层：优先级排序
    filteredEntries = this.applyPrioritySort(filteredEntries);

    return filteredEntries;
  }

  private async applyBasicFilters(entries: WorldbookEntry[], context: ActivationContext, mode: FilterMode): Promise<WorldbookEntry[]> {
    return entries.filter(entry => {
      // 启用状态检查
      if (!entry.enabled) return false;

      // 递归排除检查
      if (mode === 'recursion' && entry.excludeRecursion) return false;

      // 延迟递归检查
      if (mode !== 'recursion' && entry.delayUntilRecursion) return false;

      // 角色过滤检查
      if (!this.checkCharacterFilter(entry, context)) return false;

      return true;
    });
  }

  private async applyTimedEffectsFilter(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    const { timedEffects } = context.runtimeData;

    return entries.filter(entry => {
      const effects = timedEffects[entry.id];
      if (!effects) return true;

      // Cooldown检查
      if (effects.cooldown > 0) return false;

      // Delay检查
      if (effects.delay > 0) return false;

      return true;
    });
  }

  private async applyInclusionGroupFilter(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    const groupedEntries = this.groupEntriesByGroup(entries);
    const selectedEntries: WorldbookEntry[] = [];

    for (const [groupName, groupEntries] of groupedEntries) {
      if (groupName === null) {
        // 无分组条目直接添加
        selectedEntries.push(...groupEntries);
      } else {
        // 分组条目按权重选择
        const selectedFromGroup = this.selectFromGroup(groupEntries);
        selectedEntries.push(...selectedFromGroup);
      }
    }

    return selectedEntries;
  }

  private selectFromGroup(entries: WorldbookEntry[]): WorldbookEntry[] {
    // 按组权重排序
    const sortedEntries = entries.sort((a, b) => (b.groupWeight || 100) - (a.groupWeight || 100));
    
    // 如果有覆盖设置，只选择第一个
    if (sortedEntries.some(entry => entry.groupOverride)) {
      return [sortedEntries[0]];
    }

    // 否则返回所有条目
    return sortedEntries;
  }

  private applyPrioritySort(entries: WorldbookEntry[]): WorldbookEntry[] {
    return entries.sort((a, b) => (a.order || 100) - (b.order || 100));
  }
}
```

### 4. 主控制器流程实现

#### WorldbookActivationService核心流程
```typescript
// src/server/services/worldbook/activation-service.ts
export class WorldbookActivationService {
  constructor(
    private db: LobeChatDatabase,
    private userId: string
  ) {
    this.dataManager = new DataManager(this.db, this.userId);
    this.engines = new EngineCollection(this.dataManager);
  }

  async activate(params: { messages: OpenAIChatMessage[]; topicId?: string }): Promise<WorldbookActivationResult> {
    const startTime = Date.now();

    // 1. 检查Agent配置世界书
    const hasWorldbook = await this.checkAgentWorldbookConfig(params);
    if (!hasWorldbook) {
      return this.getEmptyResult(startTime);
    }

    // 2. 初始化激活上下文
    const context = await this.initializeContext(params);

    // 3. 执行主激活流程（五种模式并行）
    const mainEntries = await this.executeMainActivation(context);

    // 4. 统一过滤引擎处理
    const filteredEntries = await this.engines.filter.process(mainEntries, context, 'main');

    // 5. 预算控制
    const budgetControlledEntries = await this.engines.budget.control(filteredEntries, context);

    // 6. 递归处理（仅keyword模式）
    const recursionEntries = await this.processRecursion(budgetControlledEntries, context);

    // 7. 最小激活数处理（仅keyword模式）
    const minActivationEntries = await this.processMinActivation(recursionEntries, context);

    // 8. 概率检查（overflow或final模式）
    const finalEntries = await this.engines.probability.check(minActivationEntries, context, 'final');

    // 9. 更新时间效果和运行时数据
    await this.engines.timedEffects.update(finalEntries, context);
    await this.dataManager.saveRuntimeData(context.topicId!, context.runtimeData);

    return {
      activatedEntries: finalEntries,
      processingTime: Date.now() - startTime,
      metadata: this.buildMetadata(context, finalEntries)
    };
  }

  private async executeMainActivation(context: ActivationContext): Promise<WorldbookEntry[]> {
    // 并行执行五种激活模式
    const [constantEntries, decoratorEntries, keywordEntries, vectorEntries, stickyEntries] = await Promise.all([
      this.activateConstant(context),
      this.activateDecorator(context),
      this.activateKeyword(context, 'main'),
      this.activateVector(context),
      this.activateSticky(context)
    ]);

    // 合并并去重
    return this.mergeAndDeduplicate([
      ...constantEntries,
      ...decoratorEntries,
      ...keywordEntries,
      ...vectorEntries,
      ...stickyEntries
    ]);
  }

  // 五种激活模式的具体实现
  private async activateConstant(context: ActivationContext): Promise<WorldbookEntry[]> {
    return this.dataManager.getConstantEntries();
  }

  private async activateDecorator(context: ActivationContext): Promise<WorldbookEntry[]> {
    const decoratorTypes = this.extractDecorators(context.scanText);
    if (decoratorTypes.length === 0) return [];

    return this.dataManager.getDecoratorEntries(decoratorTypes);
  }

  private async activateKeyword(context: ActivationContext, mode: ActivationMode): Promise<WorldbookEntry[]> {
    return this.engines.keyword.activate(context, mode);
  }

  private async activateVector(context: ActivationContext): Promise<WorldbookEntry[]> {
    const query = this.buildVectorQuery(context.scanText);
    return this.dataManager.getVectorEntries(query, 10);
  }

  private async activateSticky(context: ActivationContext): Promise<WorldbookEntry[]> {
    const stickyIds = await this.dataManager.getStickyEntryIds(context.runtimeData);
    if (stickyIds.length === 0) return [];

    return this.dataManager.getEntriesByIds(stickyIds);
  }

  private async processRecursion(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    if (!this.shouldProcessRecursion(context)) {
      return entries;
    }

    const recursionBuffer = this.buildRecursionBuffer(entries);
    const recursionContext = { ...context, recursionBuffer };
    
    const recursionEntries = await this.engines.keyword.activate(recursionContext, 'recursion');
    const filteredRecursionEntries = await this.engines.filter.process(recursionEntries, recursionContext, 'recursion');
    
    return this.mergeAndDeduplicate([...entries, ...filteredRecursionEntries]);
  }

  private async processMinActivation(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    const minActivationCount = this.getMinActivationCount(context);
    if (entries.length >= minActivationCount) {
      return entries;
    }

    let currentEntries = [...entries];
    let depth = context.minActivationDepth || 1;
    const maxDepth = 10; // 最大扫描深度

    while (currentEntries.length < minActivationCount && depth <= maxDepth) {
      const extendedContext = { ...context, minActivationDepth: depth };
      const additionalEntries = await this.engines.keyword.activate(extendedContext, 'minActivation');
      const filteredAdditional = await this.engines.filter.process(additionalEntries, extendedContext, 'minActivation');
      
      currentEntries = this.mergeAndDeduplicate([...currentEntries, ...filteredAdditional]);
      depth++;
    }

    return currentEntries;
  }
}
```

### 5. 数据管理器实现

#### DataManager核心方法
```typescript
// src/server/services/worldbook/data-manager.ts
export class DataManager {
  constructor(
    private db: LobeChatDatabase,
    private userId: string
  ) {}

  async initializeContext(params: { messages: OpenAIChatMessage[]; topicId?: string }): Promise<ActivationContext> {
    // 获取消息计数
    const messageCount = await this.getMessageCount(params.topicId);

    // 获取运行时数据
    const runtimeData = await this.getWorldbookRuntimeData(params.topicId);

    // 清理过期时间效果
    this.cleanupExpiredEffects(runtimeData, messageCount);

    // 构建扫描文本
    const scanText = this.extractMessagesText(params.messages);

    // 获取用户信息
    const userProfile = await this.getUserProfile();

    return {
      messages: params.messages,
      topicId: params.topicId,
      messageCount,
      runtimeData,
      scanText,
      userId: this.userId,
      userProfile
    };
  }

  async getMessageCount(topicId?: string): Promise<number> {
    if (!topicId) return 0;

    const result = await this.db
      .select({ messageCount: topics.messageCount })
      .from(topics)
      .where(eq(topics.id, topicId))
      .limit(1);

    return result[0]?.messageCount || 0;
  }

  async getWorldbookRuntimeData(topicId?: string): Promise<WorldbookRuntimeData> {
    if (!topicId) return this.getDefaultRuntimeData();

    const result = await this.db
      .select({ worldbookData: topics.worldbook })
      .from(topics)
      .where(eq(topics.id, topicId))
      .limit(1);

    return result[0]?.worldbookData || this.getDefaultRuntimeData();
  }

  async saveWorldbookRuntimeData(topicId: string, data: WorldbookRuntimeData): Promise<void> {
    await this.db
      .update(topics)
      .set({ worldbook: data })
      .where(eq(topics.id, topicId));
  }

  async getConstantEntries(): Promise<WorldbookEntry[]> {
    const result = await this.db
      .select()
      .from(worldbookChunks)
      .innerJoin(worldbooks, eq(worldbookChunks.worldbookId, worldbooks.id))
      .innerJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbooks.userId, this.userId),
          eq(worldbooks.enabled, true),
          eq(worldbookChunks.enabled, true),
          eq(worldbookChunks.activationMode, 'constant')
        )
      );

    return this.transformToWorldbookEntries(result);
  }

  async getKeywordEntries(): Promise<WorldbookEntry[]> {
    const result = await this.db
      .select()
      .from(worldbookChunks)
      .innerJoin(worldbooks, eq(worldbookChunks.worldbookId, worldbooks.id))
      .innerJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbooks.userId, this.userId),
          eq(worldbooks.enabled, true),
          eq(worldbookChunks.enabled, true),
          eq(worldbookChunks.activationMode, 'keyword')
        )
      );

    return this.transformToWorldbookEntries(result);
  }

  private transformToWorldbookEntries(dbResults: any[]): WorldbookEntry[] {
    return dbResults.map(row => ({
      id: row.worldbook_chunks.id,
      title: row.worldbook_chunks.title,
      content: row.chunks.content,
      keys: row.worldbook_chunks.keys || [],
      keysSecondary: row.worldbook_chunks.keysSecondary || [],
      selectiveLogic: row.worldbook_chunks.selectiveLogic || 'and_any',
      activationMode: row.worldbook_chunks.activationMode,
      position: row.worldbook_chunks.position || 'after',
      depth: row.worldbook_chunks.depth || 4,
      role: row.worldbook_chunks.role || 'system',
      order: row.worldbook_chunks.order || 100,
      // 时间效果字段
      sticky: row.worldbook_chunks.sticky || 0,
      cooldown: row.worldbook_chunks.cooldown || 0,
      delay: row.worldbook_chunks.delay || 0,
      // 包含组字段
      groupName: row.worldbook_chunks.groupName,
      groupWeight: row.worldbook_chunks.groupWeight || 100,
      groupOverride: row.worldbook_chunks.groupOverride || false,
      // 其他字段
      probability: row.worldbook_chunks.probability || 100,
      excludeRecursion: row.worldbook_chunks.excludeRecursion || false,
      delayUntilRecursion: row.worldbook_chunks.delayUntilRecursion || false,
    }));
  }

  async getDecoratorEntries(decoratorTypes: string[]): Promise<WorldbookEntry[]> {
    if (decoratorTypes.length === 0) return [];

    const result = await this.db
      .select()
      .from(worldbookChunks)
      .innerJoin(worldbooks, eq(worldbookChunks.worldbookId, worldbooks.id))
      .innerJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbooks.userId, this.userId),
          eq(worldbooks.enabled, true),
          eq(worldbookChunks.enabled, true),
          eq(worldbookChunks.activationMode, 'decorator'),
          // 检查decorators字段是否包含任何指定的装饰器类型
          sql`${worldbookChunks.decorators} && ${decoratorTypes}`
        )
      );

    return this.transformToWorldbookEntries(result);
  }

  async getEntriesByIds(ids: string[]): Promise<WorldbookEntry[]> {
    if (ids.length === 0) return [];

    const result = await this.db
      .select()
      .from(worldbookChunks)
      .innerJoin(worldbooks, eq(worldbookChunks.worldbookId, worldbooks.id))
      .innerJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbooks.userId, this.userId),
          eq(worldbooks.enabled, true),
          eq(worldbookChunks.enabled, true),
          inArray(worldbookChunks.id, ids)
        )
      );

    return this.transformToWorldbookEntries(result);
  }

  private getDefaultRuntimeData(): WorldbookRuntimeData {
    return {
      timedEffects: {},
      activationHistory: [],
      lastActivation: 0,
      recursionDepth: 0,
      budgetUsed: 0
    };
  }
}
```

### 6. 预算控制引擎实现

#### BudgetEngine token估算和控制
```typescript
// src/server/services/worldbook/engines/budget-engine.ts
export class BudgetEngine {
  private readonly DEFAULT_BUDGET = 2000; // 默认token预算
  private readonly CHINESE_CHAR_RATIO = 1.5; // 中文字符token比例
  private readonly ENGLISH_CHAR_RATIO = 4; // 英文字符token比例

  async control(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    const budget = this.getBudget(context);
    const sortedEntries = this.sortByPriority(entries);

    let usedTokens = 0;
    const selectedEntries: WorldbookEntry[] = [];
    const overflowEntries: WorldbookEntry[] = [];

    for (const entry of sortedEntries) {
      const entryTokens = this.estimateTokens(entry.content);

      if (usedTokens + entryTokens <= budget) {
        selectedEntries.push(entry);
        usedTokens += entryTokens;
      } else {
        overflowEntries.push(entry);
      }
    }

    // 为溢出条目设置cooldown
    if (overflowEntries.length > 0) {
      await this.setCooldownForOverflow(overflowEntries, context);
    }

    // 更新预算使用情况
    context.runtimeData.budgetUsed = usedTokens;

    return selectedEntries;
  }

  private estimateTokens(text: string): number {
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = text.length - chineseChars;

    return Math.ceil(chineseChars / this.CHINESE_CHAR_RATIO + englishChars / this.ENGLISH_CHAR_RATIO);
  }

  private sortByPriority(entries: WorldbookEntry[]): WorldbookEntry[] {
    return [...entries].sort((a, b) => (a.order || 100) - (b.order || 100));
  }

  private getBudget(context: ActivationContext): number {
    // 可以从配置中获取，这里使用默认值
    return this.DEFAULT_BUDGET;
  }

  private async setCooldownForOverflow(entries: WorldbookEntry[], context: ActivationContext): Promise<void> {
    for (const entry of entries) {
      if (!context.runtimeData.timedEffects[entry.id]) {
        context.runtimeData.timedEffects[entry.id] = { sticky: 0, cooldown: 0, delay: 0 };
      }
      context.runtimeData.timedEffects[entry.id].cooldown = entry.cooldown || 1;
    }
  }
}
```

### 7. 概率检查引擎实现

#### ProbabilityEngine两种模式
```typescript
// src/server/services/worldbook/engines/probability-engine.ts
export class ProbabilityEngine {
  async check(entries: WorldbookEntry[], context: ActivationContext, mode: 'overflow' | 'final'): Promise<WorldbookEntry[]> {
    const filteredEntries: WorldbookEntry[] = [];

    for (const entry of entries) {
      const probability = this.calculateProbability(entry, context, mode);
      const randomValue = Math.random() * 100;

      if (randomValue <= probability) {
        filteredEntries.push(entry);
      }
    }

    return filteredEntries;
  }

  private calculateProbability(entry: WorldbookEntry, context: ActivationContext, mode: 'overflow' | 'final'): number {
    let baseProbability = entry.probability || 100;

    if (mode === 'overflow') {
      // 溢出模式下降低概率
      baseProbability *= 0.5;
    }

    // 可以根据其他因素调整概率
    // 例如：激活历史、时间效果等

    return Math.max(0, Math.min(100, baseProbability));
  }
}
```

### 8. 时间效果引擎实现

#### TimedEffectsEngine状态管理
```typescript
// src/server/services/worldbook/engines/timed-effects-engine.ts
export class TimedEffectsEngine {
  async update(activatedEntries: WorldbookEntry[], context: ActivationContext): Promise<void> {
    const { timedEffects } = context.runtimeData;

    // 1. 减少所有条目的时间效果计数
    this.decrementAllEffects(timedEffects);

    // 2. 为新激活的条目设置时间效果
    for (const entry of activatedEntries) {
      this.setTimedEffects(entry, timedEffects);
    }

    // 3. 清理过期的时间效果
    this.cleanupExpiredEffects(timedEffects);

    // 4. 更新激活历史
    this.updateActivationHistory(activatedEntries, context.runtimeData);
  }

  private decrementAllEffects(timedEffects: Record<string, any>): void {
    for (const entryId in timedEffects) {
      const effects = timedEffects[entryId];

      if (effects.sticky > 0) effects.sticky--;
      if (effects.cooldown > 0) effects.cooldown--;
      if (effects.delay > 0) effects.delay--;
    }
  }

  private setTimedEffects(entry: WorldbookEntry, timedEffects: Record<string, any>): void {
    if (!timedEffects[entry.id]) {
      timedEffects[entry.id] = { sticky: 0, cooldown: 0, delay: 0 };
    }

    const effects = timedEffects[entry.id];

    // 设置sticky效果
    if (entry.sticky && entry.sticky > 0) {
      effects.sticky = entry.sticky;
    }

    // 设置cooldown效果（如果条目有cooldown配置）
    if (entry.cooldown && entry.cooldown > 0) {
      effects.cooldown = entry.cooldown;
    }
  }

  private cleanupExpiredEffects(timedEffects: Record<string, any>): void {
    for (const entryId in timedEffects) {
      const effects = timedEffects[entryId];

      // 如果所有效果都为0，删除该条目
      if (effects.sticky <= 0 && effects.cooldown <= 0 && effects.delay <= 0) {
        delete timedEffects[entryId];
      }
    }
  }

  private updateActivationHistory(entries: WorldbookEntry[], runtimeData: WorldbookRuntimeData): void {
    const newIds = entries.map(entry => entry.id);

    // 添加新激活的条目ID到历史记录
    runtimeData.activationHistory.push(...newIds);

    // 保持历史记录在合理长度内（例如最近100个）
    if (runtimeData.activationHistory.length > 100) {
      runtimeData.activationHistory = runtimeData.activationHistory.slice(-100);
    }

    // 更新最后激活时间
    runtimeData.lastActivation = Date.now();
  }
}
```

### 9. API接口实现

#### 后端API路由
```typescript
// src/app/(backend)/webapi/chat/enhanced-messages/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/libs/next-auth';
import { WorldbookActivationService } from '@/server/services/worldbook/activation-service';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';

export async function POST(req: NextRequest) {
  try {
    // 1. 身份验证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. 解析请求参数
    const { messages, topicId } = await req.json();

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: 'Invalid messages' }, { status: 400 });
    }

    // 3. 初始化服务（全部封装在激活模块内部）
    const db = await serverDatabase();
    const activationService = new WorldbookActivationService(db, session.user.id);

    // 4. 执行世界书激活
    const activationResult = await activationService.activate({
      messages,
      topicId
    });

    // 5. 处理预设并注入世界书内容
    const enhancedMessages = await this.processEnhancedPresets({
      messages,
      activatedEntries: activationResult.activatedEntries
    });

    // 6. 返回结果
    return NextResponse.json({
      enhancedMessages,
      metadata: {
        activatedCount: activationResult.activatedEntries.length,
        processingTime: activationResult.processingTime,
        ...activationResult.metadata
      }
    });

  } catch (error) {
    console.error('Enhanced messages processing failed:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function processEnhancedPresets(params: {
  messages: OpenAIChatMessage[];
  activatedEntries: WorldbookEntry[];
}): Promise<OpenAIChatMessage[]> {
  const { messages, activatedEntries } = params;

  // 1. 注入世界书内容
  const messagesWithWorldbook = injectWorldbookContent(messages, activatedEntries);

  // 2. 处理原有预设逻辑（移植现有逻辑）
  const enhancedMessages = processOriginalPresets(messagesWithWorldbook);

  return enhancedMessages;
}

function injectWorldbookContent(messages: OpenAIChatMessage[], entries: WorldbookEntry[]): OpenAIChatMessage[] {
  if (entries.length === 0) return messages;

  const messagesCopy = [...messages];

  // 按位置分组世界书条目
  const groupedEntries = groupEntriesByPosition(entries);

  // 注入到系统消息位置
  if (groupedEntries.system.length > 0) {
    const systemContent = groupedEntries.system.map(entry => entry.content).join('\n\n');

    if (messagesCopy[0]?.role === 'system') {
      messagesCopy[0].content += '\n\n' + systemContent;
    } else {
      messagesCopy.unshift({
        role: 'system',
        content: systemContent
      });
    }
  }

  // 注入到指定深度
  for (const entry of groupedEntries.atDepth) {
    const targetIndex = Math.min(entry.depth || 4, messagesCopy.length);
    messagesCopy.splice(targetIndex, 0, {
      role: entry.role || 'system',
      content: entry.content
    });
  }

  // 注入到末尾
  if (groupedEntries.after.length > 0) {
    const afterContent = groupedEntries.after.map(entry => entry.content).join('\n\n');
    messagesCopy.push({
      role: 'system',
      content: afterContent
    });
  }

  return messagesCopy;
}

function groupEntriesByPosition(entries: WorldbookEntry[]) {
  return {
    system: entries.filter(entry => entry.position === 'system'),
    atDepth: entries.filter(entry => entry.position === 'at_depth'),
    after: entries.filter(entry => entry.position === 'after' || !entry.position)
  };
}
```

### 10. 前端集成实现

#### ChatService修改
```typescript
// src/services/chat.ts (修改现有文件)
export class ChatService {
  // ... 现有方法 ...

  private processMessagesEnhanced = async (
    messages: OpenAIChatMessage[],
    options?: {
      topicId?: string;
    }
  ): Promise<OpenAIChatMessage[]> => {
    // 如果没有topicId，回退到原有处理
    if (!options?.topicId) {
      return this.processMessagesPreset({ messages, model: 'default' });
    }

    try {
      // 调用后端增强API
      const response = await fetch('/api/chat/enhanced-messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages,
          topicId: options.topicId
        })
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }

      const result = await response.json();

      // 记录激活信息（可选）
      if (result.metadata?.activatedCount > 0) {
        console.log(`Worldbook activated ${result.metadata.activatedCount} entries in ${result.metadata.processingTime}ms`);
      }

      return result.enhancedMessages;

    } catch (error) {
      console.warn('Worldbook activation failed, falling back to preset processing:', error);

      // 错误回退：使用原有预设处理
      return this.processMessagesPreset({ messages, model: 'default' });
    }
  };

  // 修改现有的createAssistantMessage方法
  createAssistantMessage = async (
    params: CreateAssistantMessageParams,
    options?: FetchOptions
  ): Promise<Response> => {
    // ... 现有代码 ...

    // ============  1. preprocess placeholder variables   ============ //
    const parsedMessages = parsePlaceholderVariablesMessages(messages);

    // ============  2. preprocess messages   ============ //
    let oaiMessages = await this.processMessages({
      messages: parsedMessages,
      model: payload.model,
      provider: payload.provider!,
      tools: pluginIds,
    }, options);

    // ============  3. enhanced messages processing (NEW)  ============ //
    oaiMessages = await this.processMessagesEnhanced(oaiMessages, {
      topicId: options?.trace?.topicId
    });

    // ============  4. get chat completion   ============ //
    return this.getChatCompletion(payload, oaiMessages, options);
  };
}
```

### 11. 数据库扩展实现

#### Schema扩展
```typescript
// src/database/schemas/topic.ts (扩展现有文件)
import { jsonb, integer } from 'drizzle-orm/pg-core';

export const topics = pgTable('topics', {
  // ... 现有字段 ...

  // 新增字段
  messageCount: integer('message_count').default(0),
  worldbook: jsonb('worldbook').$type<WorldbookRuntimeData>().default({}),
});
```

#### Model扩展
```typescript
// src/database/models/topic.ts (扩展现有文件)
export class TopicModel {
  // ... 现有方法 ...

  async getMessageCount(topicId: string): Promise<number> {
    const result = await this.db
      .select({ messageCount: topics.messageCount })
      .from(topics)
      .where(eq(topics.id, topicId))
      .limit(1);

    return result[0]?.messageCount || 0;
  }

  async incrementMessageCount(topicId: string): Promise<void> {
    await this.db
      .update(topics)
      .set({ messageCount: sql`${topics.messageCount} + 1` })
      .where(eq(topics.id, topicId));
  }

  async getWorldbookRuntimeData(topicId: string): Promise<WorldbookRuntimeData> {
    const result = await this.db
      .select({ worldbook: topics.worldbook })
      .from(topics)
      .where(eq(topics.id, topicId))
      .limit(1);

    return result[0]?.worldbook || {
      timedEffects: {},
      activationHistory: [],
      lastActivation: 0,
      recursionDepth: 0,
      budgetUsed: 0
    };
  }

  async saveWorldbookRuntimeData(topicId: string, data: WorldbookRuntimeData): Promise<void> {
    await this.db
      .update(topics)
      .set({ worldbook: data })
      .where(eq(topics.id, topicId));
  }
}
```

### 12. 部署和测试指导

#### 数据库迁移
```sql
-- 添加新字段到topics表
ALTER TABLE topics
ADD COLUMN message_count INTEGER DEFAULT 0,
ADD COLUMN worldbook JSONB DEFAULT '{}';

-- 创建索引以提高查询性能
CREATE INDEX idx_topics_message_count ON topics(message_count);
CREATE INDEX idx_worldbook_chunks_activation_mode ON worldbook_chunks(activation_mode);
CREATE INDEX idx_worldbook_chunks_enabled ON worldbook_chunks(enabled);
```

#### 环境变量配置
```env
# 世界书激活相关配置
WORLDBOOK_DEFAULT_BUDGET=2000
WORLDBOOK_MAX_RECURSION_DEPTH=5
WORLDBOOK_VECTOR_SIMILARITY_THRESHOLD=0.7
WORLDBOOK_ENABLE_DEBUG_LOGGING=false
```

#### 测试用例示例
```typescript
// __tests__/worldbook-activation.test.ts
describe('WorldbookActivationService', () => {
  let service: WorldbookActivationService;
  let mockDataManager: jest.Mocked<DataManager>;

  beforeEach(() => {
    mockDataManager = createMockDataManager();
    const engines = new EngineCollection(mockDataManager);
    service = new WorldbookActivationService(mockDataManager, engines);
  });

  test('should activate constant entries', async () => {
    const mockEntries = [createMockWorldbookEntry({ activationMode: 'constant' })];
    mockDataManager.getConstantEntries.mockResolvedValue(mockEntries);

    const result = await service.activate({
      messages: [{ role: 'user', content: 'Hello' }],
      sessionId: 'test-session',
      topicId: 'test-topic'
    });

    expect(result.activatedEntries).toHaveLength(1);
    expect(result.activatedEntries[0].activationMode).toBe('constant');
  });

  test('should handle keyword matching', async () => {
    const mockEntries = [createMockWorldbookEntry({
      activationMode: 'keyword',
      keys: ['test', 'hello'],
      selectiveLogic: 'and_any'
    })];
    mockDataManager.getKeywordEntries.mockResolvedValue(mockEntries);

    const result = await service.activate({
      messages: [{ role: 'user', content: 'Hello world' }],
      sessionId: 'test-session',
      topicId: 'test-topic'
    });

    expect(result.activatedEntries).toHaveLength(1);
  });

  test('should respect budget limits', async () => {
    const mockEntries = Array.from({ length: 10 }, (_, i) =>
      createMockWorldbookEntry({
        content: 'A'.repeat(1000), // 大内容条目
        order: i
      })
    );
    mockDataManager.getConstantEntries.mockResolvedValue(mockEntries);

    const result = await service.activate({
      messages: [{ role: 'user', content: 'Hello' }],
      sessionId: 'test-session',
      topicId: 'test-topic'
    });

    // 应该因为预算限制而激活较少的条目
    expect(result.activatedEntries.length).toBeLessThan(10);
  });
});
```

### 13. 性能优化建议

#### 缓存策略
```typescript
// 实现查询结果缓存
class CacheManager {
  private cache = new Map<string, { data: any; expiry: number }>();
  private readonly TTL = 5 * 60 * 1000; // 5分钟

  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.TTL
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item || Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    return item.data;
  }
}
```

#### 监控和日志
```typescript
// 性能监控
class PerformanceMonitor {
  static logActivation(result: WorldbookActivationResult): void {
    console.log(`[Worldbook] Activated ${result.activatedEntries.length} entries`, {
      processingTime: result.processingTime,
      metadata: result.metadata
    });
  }

  static logError(error: Error, context: any): void {
    console.error('[Worldbook] Activation failed:', {
      error: error.message,
      stack: error.stack,
      context
    });
  }
}
```

## 🎯 总结

本技术实现文档提供了v5架构方案的详细实现指导，包括：

1. **核心接口设计**：统一的引擎接口和数据结构
2. **关键算法实现**：关键词匹配、过滤机制、预算控制等
3. **数据流处理**：完整的激活流程和数据管理
4. **API集成**：后端接口和前端集成的具体实现
5. **部署指导**：数据库迁移、配置和测试

这个实现方案完全基于v5架构设计，提供了生产就绪的代码实现指导。
