import { FileItem } from '@/types/files';
import { KnowledgeBaseItem } from '@/types/knowledgeBase';
import { FewShots, LLMParams } from '@/types/llm';
import { Worldbook } from '@/types/worldbook';

import { LobeAgentChatConfig } from './chatConfig';

export type TTSServer = 'openai' | 'edge' | 'microsoft';

export interface LobeAgentTTSConfig {
  showAllLocaleVoice?: boolean;
  sttLocale: 'auto' | string;
  ttsService: TTSServer;
  voice: {
    edge?: string;
    microsoft?: string;
    openai: string;
  };
}
export interface LobeAgentPromptConfig {
  character_version: string;
  injection_prompt: {
    depth: number;
    prompt: string;
    role: string;
  };
  main_prompt: string;
  mes_example: string;
  personality: string;
  post_history_instructions: string;
  scenario: string;
  talkativeness: number;
  world: string;
}
export interface LobeAgentWorldBookMeta {
  entries: object[];
  name: string;
}

export interface LobeAgentConfig {
  authorAvatar: string;
  authorName: string;
  authorNotes: string;
  authorUid: string;
  chatConfig: LobeAgentChatConfig;
  fewShots?: FewShots;
  files?: FileItem[];
  id?: string;
  /**
   * knowledge bases
   */
  knowledgeBases?: KnowledgeBaseItem[];
  /**
   * 角色所使用的语言模型
   * @default gpt-4o-mini
   */
  model: string;

  /**
   * 开场白
   */
  openingMessage?: string;
  /**
   * 开场问题
   */
  openingQuestions?: string[];

  /**
   * 语言模型参数
   */
  params: LLMParams;
  /**
   * 启用的插件
   */
  plugins?: string[];
  // 预设
  preset: string;
  promptConfig: LobeAgentPromptConfig;
  /**
   *  模型供应商
   */
  provider?: string;
  roleFirstMsgs: string[];
  /**
   * 系统角色
   */
  systemRole: string;
  title: string;
  /**
   * 语音服务
   */
  tts: LobeAgentTTSConfig;

  worldBook: LobeAgentWorldBookMeta;

  /**
   * worldbooks
   */
  worldbooks?: Worldbook[];
}

export type LobeAgentConfigKeys =
  | keyof LobeAgentConfig
  | ['params', keyof LobeAgentConfig['params']];

export * from './chatConfig';
