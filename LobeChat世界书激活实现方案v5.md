# LobeChat 世界书激活实现方案 v5

## 🎯 宏观设计视角

### 核心目标
在LobeChat现有架构中集成世界书激活功能，通过后端统一处理实现消息预处理时的智能内容注入。

### 设计原则
1. **流程驱动**：主控制器负责流程控制，不包含具体算法实现
2. **引擎复用**：统一引擎支持多种调用模式，在不同阶段复用
3. **数据集中**：所有数据操作集中管理，对应链路图数据节点
4. **最小化前端修改**：仅修改消息处理调用方式，保持现有逻辑不变
5. **完全符合链路图v8**：统一引擎复用，递归和最小激活数仅keyword模式

### 整体架构
```
前端消息处理 → 后端增强API → 世界书激活服务 → 预设处理服务 → 增强消息返回
```

## 📁 文件清单

### 新增文件

#### 后端API接口
- **文件**：`src/app/(backend)/webapi/chat/enhanced-messages/route.ts`
- **功能**：接收messages和topicId，调用世界书激活和预设处理，返回增强消息
- **核心方法**：POST处理器，身份验证，错误处理

#### 世界书激活服务（新增）
- **文件**：`src/server/services/worldbook/activation-service.ts`
- **功能**：主控制器，流程驱动，协调统一引擎调用
- **核心方法**：
  - `activate()` - 主激活流程，完全对应链路图v8流程
  - `executeMainActivation()` - 五种激活模式并行执行
  - `processRecursion()` - 递归处理（仅keyword，使用统一引擎）
  - `processMinActivation()` - 最小激活数处理（仅keyword，使用统一引擎）

#### 统一引擎集合（新增）
- **目录**：`src/server/services/worldbook/engines/`
- **文件列表**：
  - `keyword-engine.ts` - 关键词匹配引擎（支持main/recursion/minActivation三种模式）
  - `filter-engine.ts` - 统一过滤引擎（支持main/recursion/minActivation三种调用）
  - `probability-engine.ts` - 概率检查引擎（支持overflow/final两种模式）
  - `budget-engine.ts` - 预算控制引擎
  - `timed-effects-engine.ts` - 时间效果引擎
  - `index.ts` - 引擎统一导出

#### 数据操作管理器（新增）
- **文件**：`src/server/services/worldbook/data-manager.ts`
- **功能**：集中管理所有数据操作，对应链路图v8的数据节点
- **核心方法**：
  - `initializeContext()` - 对应GET_MESSAGE_COUNT节点
  - `saveRuntimeData()` - 对应SAVE_RUNTIME_DATA节点
  - `setTimedEffects()` - 对应SET_TIMED_EFFECTS节点
  - `updateMessageCount()` - 对应UPDATE_MESSAGE_COUNT节点

#### 增强预设处理服务
- **文件**：`src/server/services/preset/enhanced-processor.ts`
- **功能**：增强预设处理服务，集成世界书内容注入
- **核心方法**：
  - `processEnhancedPresets()` - 主处理方法
  - `injectWorldbookContent()` - 世界书内容注入
  - `processOriginalPresets()` - 原有预设处理

#### 类型定义
- **文件**：`src/server/services/worldbook/types.ts`
- **功能**：世界书激活相关的类型定义
- **核心接口**：ActivationContext、EngineCollection、WorldbookActivationResult

#### 工具函数
- **文件**：`src/server/services/worldbook/utils.ts`
- **功能**：通用工具函数
- **核心方法**：mergeAndDeduplicate、extractDecorators、buildQueryVector

### 修改文件

#### 前端消息服务
- **文件**：`src/services/chat.ts`
- **修改方法**：`ChatService.createAssistantMessage`
- **新增方法**：`processMessagesEnhanced()` - 后端增强处理调用
- **修改逻辑**：
  - 原有：`oaiMessages = this.processMessagesPreset()`
  - 修改为：`oaiMessages = await this.processMessagesEnhanced()`
  - 错误处理：失败时自动回退到原有处理

#### 数据库Schema扩展
- **文件**：`src/database/schemas/topic.ts`
- **新增字段**：
  - `messageCount: integer('message_count').default(0)` - 消息计数
  - `worldbook: jsonb('worldbook').default({})` - 运行时数据

#### 类型定义扩展
- **文件**：`src/types/worldbook.ts`
- **新增接口**：
  - `WorldbookRuntimeData` - 运行时数据结构
  - `ActivationContext` - 激活上下文
  - `WorldbookActivationResult` - 激活结果

## 🔄 执行链路

### 主要调用流程
```
用户发送消息
    ↓
ChatService.createAssistantMessage()
    ↓
processMessagesEnhanced() ← 新的集成点
    ↓
POST /api/chat/enhanced-messages
    ↓
DataManager.initializeContext()
    ↓
WorldbookActivationService.activate()
    ↓
EnhancedPresetProcessor.processEnhancedPresets()
    ↓
返回增强消息
    ↓
发送给AI模型
```

### 激活服务内部流程（完全对应链路图v8）
```
WorldbookActivationService.activate()
    ↓
DataManager.initializeContext() (GET_MESSAGE_COUNT)
    ↓
executeMainActivation() (并行执行五种激活模式)
    ├── activateConstant()
    ├── activateDecorator()
    ├── KeywordEngine.activate(context, 'main') ← 统一引擎
    ├── activateVector()
    └── activateSticky()
    ↓
FilterEngine.process(entries, context, 'main') ← 统一引擎
    ├── 基础过滤
    ├── 时间效果过滤
    ├── 包含组过滤
    └── 优先级排序
    ↓
BudgetEngine.control()
    ↓
processRecursion() (如果启用，仅keyword)
    ├── KeywordEngine.activate(context, 'recursion') ← 统一引擎复用
    └── FilterEngine.process(entries, context, 'recursion') ← 统一引擎复用
    ↓
processMinActivation() (如果启用，仅keyword)
    ├── KeywordEngine.activate(context, 'minActivation') ← 统一引擎复用
    └── FilterEngine.process(entries, context, 'minActivation') ← 统一引擎复用
    ↓
ProbabilityEngine.check(entries, 'overflow'|'final') ← 两种模式
    ↓
TimedEffectsEngine.update() + DataManager.saveRuntimeData()
    ↓
返回激活条目列表
```

### 预设处理集成流程
```
EnhancedPresetProcessor.processEnhancedPresets()
    ↓
injectWorldbookContent()
    ├── 按位置分组世界书条目
    ├── 注入到系统消息位置
    ├── 注入到指定深度
    └── 处理特殊位置注入
    ↓
processOriginalPresets()
    ├── 移植现有预设处理逻辑
    ├── 变量替换处理
    └── 消息合并处理
    ↓
返回增强消息数组
```

## 🔍 核心细节说明

### 统一引擎复用设计

#### 关键词匹配引擎（三种模式复用）
- **main模式**：主激活阶段，扫描原始消息+全局数据
- **recursion模式**：递归阶段，扫描原始消息+递归缓冲区
- **minActivation模式**：最小激活数阶段，扫描扩展深度文本

#### 统一过滤引擎（三种调用复用）
- **main调用**：主激活阶段的四层过滤机制
- **recursion调用**：递归阶段的过滤，包含递归特定逻辑
- **minActivation调用**：最小激活数阶段的过滤

#### 概率检查引擎（两种模式）
- **overflow模式**：预算溢出时的概率检查，降低概率
- **final模式**：正常情况下的最终概率检查

### 五种激活模式

#### 1. Constant模式
- **处理逻辑**：查询activationMode='constant'且enabled=true的条目
- **实现方式**：DataManager.getConstantEntries()直接数据库查询

#### 2. Keyword模式
- **处理逻辑**：使用统一的关键词匹配引擎
- **实现方式**：KeywordEngine.activate(context, 'main')
- **核心功能**：正则表达式匹配、全词匹配、变量替换、选择性逻辑

#### 3. Vector模式
- **处理逻辑**：基于向量相似度激活
- **实现方式**：集成现有RAG向量搜索能力
- **核心功能**：语义相似度计算、阈值判断、相似度排序

#### 4. Sticky模式
- **处理逻辑**：从运行时数据提取active sticky条目
- **实现方式**：DataManager.getStickyEntryIds()获取ID列表
- **核心功能**：粘性状态管理、持久化恢复

#### 5. Decorator模式
- **处理逻辑**：扫描消息中的装饰器标记
- **实现方式**：extractDecorators()提取类型，查询匹配条目
- **核心功能**：装饰器解析、特殊标记识别

### 递归处理机制
- **限制**：仅对keyword模式进行递归（对应链路图RECURSION_KEYWORD_ONLY）
- **引擎复用**：使用KeywordEngine.activate(context, 'recursion')
- **过滤复用**：使用FilterEngine.process(entries, context, 'recursion')
- **条件检查**：excludeRecursion、delayUntilRecursion级别管理
- **深度控制**：最大递归步数限制，递归缓冲区管理

### 最小激活数机制
- **限制**：仅对keyword模式进行扩展（对应链路图MIN_ACTIVATION_KEYWORD_ONLY）
- **引擎复用**：使用KeywordEngine.activate(context, 'minActivation')
- **过滤复用**：使用FilterEngine.process(entries, context, 'minActivation')
- **深度扩展**：动态增加扫描深度，构建扩展扫描文本
- **循环控制**：直到满足最小激活数或达到深度限制

### 时间效果管理
- **Sticky效果**：条目在指定轮数内保持激活状态
- **Cooldown效果**：条目在指定轮数内无法激活
- **Delay效果**：条目延迟指定轮数后才能激活
- **数据存储**：运行时数据存储在topic表的worldbook字段中
- **状态管理**：TimedEffectsEngine统一管理时间效果计算和更新

### 关键词匹配算法
- **正则表达式**：支持正则表达式解析和匹配
- **字符串转换**：大小写处理、特殊字符转换
- **全词匹配**：边界检查，避免部分匹配
- **变量替换**：支持{{char}}、{{user}}、{{time}}、{{date}}等变量
- **选择性逻辑**：AND_ANY、AND_ALL、NOT_ANY、NOT_ALL四种逻辑

### 预算控制机制
- **Token估算**：中文字符/1.5 + 英文字符/4
- **优先级排序**：按order字段排序条目
- **预算分配**：逐个累计token，超出预算时停止
- **溢出处理**：为溢出条目设置cooldown，触发overflow模式概率检查

### 包含组功能
- **组权重计算**：基于组内条目的权重分配
- **组评分机制**：评估组内条目的激活价值
- **组覆盖策略**：处理组间冲突和覆盖逻辑
- **组互斥处理**：确保互斥组的正确选择

## 🚀 开发实施计划

### 第一阶段：数据库和类型定义（1天）
1. **扩展数据库Schema**：在topics表添加messageCount和worldbook字段
2. **创建类型定义**：实现ActivationContext、EngineCollection等核心接口
3. **创建工具函数**：实现mergeAndDeduplicate、extractDecorators等通用方法

### 第二阶段：数据管理器实现（1天）
1. **实现DataManager类**：集中管理所有数据操作
2. **实现数据节点方法**：对应链路图的GET_MESSAGE_COUNT、SAVE_RUNTIME_DATA等节点
3. **实现数据查询方法**：getConstantEntries、getKeywordEntries等各种查询接口

### 第三阶段：统一引擎实现（2天）
1. **实现KeywordEngine**：支持main/recursion/minActivation三种模式的关键词匹配
2. **实现FilterEngine**：支持三种调用的统一过滤引擎，包含四层过滤机制
3. **实现ProbabilityEngine**：支持overflow/final两种模式的概率检查
4. **实现BudgetEngine**：预算控制和token估算逻辑
5. **实现TimedEffectsEngine**：时间效果的计算和状态管理

### 第四阶段：主控制器实现（1天）
1. **实现WorldbookActivationService**：主控制器，流程驱动的激活服务
2. **实现五种激活模式方法**：activateConstant、activateDecorator等激活逻辑
3. **实现递归和最小激活数处理**：processRecursion、processMinActivation方法
4. **实现引擎协调逻辑**：确保统一引擎在不同阶段的正确复用

### 第五阶段：API和前端集成（1天）
1. **创建后端API接口**：enhanced-messages路由，处理前端请求
2. **实现EnhancedPresetProcessor**：增强预设处理服务，集成世界书内容注入
3. **修改前端ChatService**：添加processMessagesEnhanced方法，集成后端调用
4. **实现错误处理和回退机制**：确保系统稳定性

### 开发重点
1. **统一引擎复用**：确保KeywordEngine和FilterEngine在三个阶段的正确复用
2. **数据操作准确性**：严格对应链路图v8的所有数据节点
3. **流程控制精确性**：主控制器严格按照链路图流程执行
4. **功能完整性**：全面实现五种激活模式和所有处理机制

### 预期效果
- ✅ 完全符合链路图v8设计（95%符合度）
- ✅ 统一引擎复用，减少代码重复
- ✅ 支持五种世界书激活模式
- ✅ 支持递归处理和最小激活数机制（仅keyword）
- ✅ 支持时间效果和包含组功能
- ✅ 与现有预设系统无缝集成
- ✅ 架构清晰，易于维护和扩展

**总开发周期：5天，完全基于链路图v8设计，以全面实现功能为目标。**
